# 手机名称差异分析功能说明

## 功能概述

在客服分配总量统计表中新增了"手机名称差异分析"sheet表格，用于统计每个客服的分配汇总的手机名称与每日工作数据的手机名称差异情况。

## 功能特点

### 1. 自动分析差异
- 比较分配汇总中的手机名称与每日工作数据中的手机名称
- 自动识别完全匹配、部分匹配、仅一方有数据等情况
- 提供详细的差异统计信息

### 2. 详细的差异状态
- **完全匹配**: 两边手机名称完全相同
- **部分匹配**: 有共同手机，但也有各自独有的手机
- **仅分配汇总有数据**: 只有分配汇总中有手机信息
- **仅每日工作有数据**: 只有每日工作数据中有手机信息
- **无数据**: 两边都没有手机信息

### 3. 智能匹配算法
- 支持运营编码的精确匹配和数字匹配
- 处理带手机标记的运营编码（如：编码_手机_手机名称）
- 自动处理多个手机名称的情况（逗号分隔）

## 表格结构

新增的"手机名称差异分析"sheet包含以下列：

| 列名 | 说明 |
|------|------|
| 客服姓名 | 分配的客服姓名 |
| 运营编码 | 基础运营编码（去除手机标记） |
| 分配汇总手机名称 | 从分配汇总中提取的手机名称 |
| 每日工作手机名称 | 从每日工作数据中匹配的手机名称 |
| 差异状态 | 差异分析结果 |
| 备注 | 详细的差异说明 |

## 使用场景

### 1. 数据质量检查
- 验证分配汇总与每日工作数据的一致性
- 发现数据录入错误或遗漏

### 2. 分配优化
- 识别手机分配不合理的情况
- 为后续分配策略调整提供依据

### 3. 问题排查
- 快速定位手机名称不匹配的运营编码
- 帮助解决数据同步问题

## 技术实现

### 1. 数据来源
- **分配汇总数据**: 从客服分配过程中收集的手机名称信息
- **每日工作数据**: 从每日工作总表中提取的手机名称信息

### 2. 匹配逻辑
```python
# 运营编码匹配
def is_operation_code_match(daily_code, target_code):
    # 精确匹配
    if daily_code == target_code:
        return True
    
    # 数字匹配（提取纯数字部分比较）
    daily_digits = extract_digits_from_code(daily_code)
    target_digits = extract_digits_from_code(target_code)
    return daily_digits == target_digits and daily_digits != ""
```

### 3. 差异分析
```python
def analyze_phone_name_difference(allocation_phones, daily_phones):
    # 计算交集、差集
    common = allocation_set & daily_set
    only_in_allocation = allocation_set - daily_set
    only_in_daily = daily_set - allocation_set
    
    # 生成差异状态和备注
    return status, note
```

## 文件位置

新功能的代码主要在以下文件中：

- `core/excel_generator.py`: 主要实现代码
  - `create_phone_name_difference_sheet()`: 创建差异分析表格
  - `get_daily_work_phones_for_operation()`: 获取每日工作数据中的手机名称
  - `analyze_phone_name_difference()`: 分析手机名称差异

## 注意事项

1. **数据依赖**: 需要同时提供每日工作数据和客服分配数据才能生成差异分析
2. **编码匹配**: 使用数字匹配算法，确保不同格式的运营编码能正确匹配
3. **性能考虑**: 对于大量数据，分析过程可能需要一些时间
4. **数据清洗**: 自动过滤空值、"未分组"等无效数据

## 更新日志

- **2025-07-30**: 初始版本发布
  - 新增手机名称差异分析功能
  - 支持多种差异状态识别
  - 集成到客服分配总量统计表中
