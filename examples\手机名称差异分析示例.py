#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手机名称差异分析功能示例
演示如何使用新增的手机名称差异分析功能
"""

import pandas as pd
from pathlib import Path
from core.excel_generator import ExcelGenerator

def create_sample_data():
    """创建示例数据"""
    
    # 示例分配数据
    allocated_data = {
        'sheet1': {
            'staff_name': '张三',
            'operation_code': 'OP001_手机_iPhone13',
            'row_count': 100,
            'phone_name': 'iPhone13',
            'data': pd.DataFrame({'评价内容': ['好评1', '好评2'], '评分': [5, 4]})
        },
        'sheet2': {
            'staff_name': '张三',
            'operation_code': 'OP002',
            'row_count': 80,
            'phone_name': 'Samsung S21',
            'data': pd.DataFrame({'评价内容': ['中评1', '好评3'], '评分': [3, 5]})
        },
        'sheet3': {
            'staff_name': '李四',
            'operation_code': 'OP003_手机_Xiaomi12',
            'row_count': 120,
            'phone_name': 'Xiaomi12',
            'data': pd.DataFrame({'评价内容': ['好评4', '好评5'], '评分': [5, 5]})
        }
    }
    
    # 示例每日工作数据
    daily_work_data = pd.DataFrame({
        '运营编码': ['OP001', 'OP001', 'OP002', 'OP002', 'OP003', 'OP004'],
        '手机名称': ['iPhone13', 'iPhone13', 'Samsung S21', 'Huawei P40', 'Xiaomi12', 'OPPO Find X3'],
        '工作量': [50, 50, 40, 40, 120, 60],
        '日期': ['2025-07-30'] * 6
    })
    
    # 示例客服运营编码分配
    staff_operation_codes = {
        '张三': {'OP001_手机_iPhone13', 'OP002'},
        '李四': {'OP003_手机_Xiaomi12'}
    }
    
    return allocated_data, daily_work_data, staff_operation_codes

def demonstrate_phone_difference_analysis():
    """演示手机名称差异分析功能"""
    
    print("=== 手机名称差异分析功能演示 ===\n")
    
    # 创建示例数据
    allocated_data, daily_work_df, staff_operation_codes = create_sample_data()
    
    print("1. 示例数据概览:")
    print("   分配数据:")
    for key, info in allocated_data.items():
        print(f"     - {info['staff_name']}: {info['operation_code']} (手机: {info['phone_name']})")
    
    print("\n   每日工作数据:")
    for _, row in daily_work_df.iterrows():
        print(f"     - 运营编码: {row['运营编码']}, 手机: {row['手机名称']}")
    
    # 创建Excel生成器
    def status_callback(message):
        print(f"   [状态] {message}")
    
    excel_generator = ExcelGenerator(status_callback)
    
    print("\n2. 生成差异分析报告:")
    
    # 创建输出目录
    output_path = Path("output/差异分析示例")
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 生成Excel文件（包含差异分析）
    excel_generator.generate_excel_files_with_daily_work(
        allocated_data=allocated_data,
        output_path=output_path,
        unmatched_data=[],
        staff_operation_codes=staff_operation_codes,
        daily_work_df=daily_work_df,
        operation_data=None
    )
    
    print("\n3. 预期的差异分析结果:")
    print("   张三 - OP001:")
    print("     - 分配汇总手机: iPhone13")
    print("     - 每日工作手机: iPhone13")
    print("     - 差异状态: 完全匹配")
    
    print("   张三 - OP002:")
    print("     - 分配汇总手机: Samsung S21")
    print("     - 每日工作手机: Samsung S21, Huawei P40")
    print("     - 差异状态: 部分匹配")
    
    print("   李四 - OP003:")
    print("     - 分配汇总手机: Xiaomi12")
    print("     - 每日工作手机: Xiaomi12")
    print("     - 差异状态: 完全匹配")
    
    print(f"\n4. 输出文件位置: {output_path}")
    print("   请查看 '0.客服分配总量统计.xlsx' 文件中的 '手机名称差异分析' sheet")

def analyze_difference_types():
    """分析不同类型的差异情况"""
    
    print("\n=== 差异类型分析 ===\n")
    
    excel_generator = ExcelGenerator()
    
    # 测试不同的差异情况
    test_cases = [
        {
            'name': '完全匹配',
            'allocation': ['iPhone13', 'Samsung S21'],
            'daily': ['iPhone13', 'Samsung S21'],
            'expected': '完全匹配'
        },
        {
            'name': '部分匹配',
            'allocation': ['iPhone13', 'Samsung S21'],
            'daily': ['iPhone13', 'Huawei P40'],
            'expected': '部分匹配'
        },
        {
            'name': '仅分配汇总有数据',
            'allocation': ['iPhone13'],
            'daily': [],
            'expected': '仅分配汇总有数据'
        },
        {
            'name': '仅每日工作有数据',
            'allocation': [],
            'daily': ['iPhone13'],
            'expected': '仅每日工作有数据'
        },
        {
            'name': '无数据',
            'allocation': [],
            'daily': [],
            'expected': '无数据'
        }
    ]
    
    for case in test_cases:
        status, note = excel_generator.analyze_phone_name_difference(
            case['allocation'], case['daily']
        )
        print(f"{case['name']}:")
        print(f"  分配汇总: {case['allocation']}")
        print(f"  每日工作: {case['daily']}")
        print(f"  分析结果: {status}")
        print(f"  备注: {note}")
        print(f"  预期结果: {case['expected']}")
        print(f"  匹配: {'✓' if status == case['expected'] else '✗'}")
        print()

if __name__ == "__main__":
    # 演示主要功能
    demonstrate_phone_difference_analysis()
    
    # 分析差异类型
    analyze_difference_types()
    
    print("\n=== 演示完成 ===")
    print("新功能已成功集成到系统中，每次运行数据处理时都会自动生成手机名称差异分析。")
